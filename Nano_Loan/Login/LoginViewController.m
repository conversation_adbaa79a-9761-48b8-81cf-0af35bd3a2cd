//
//  LoginViewController.m
//  Nano_Loan
//
//  Created by yongsheng ye on 2025/6/11.
//

#import "LoginViewController.h"
#import "NetworkManager.h"
#import <Masonry/Masonry.h>
#import "UIColor+Hex.h"
#import "HUD.h"
#import "RiskEventManager.h"
#import "H5WebViewController.h"
#import <MBProgressHUD/MBProgressHUD.h>

@interface LoginViewController ()
@property (nonatomic, assign) NSTimeInterval riskStartRegister;
@end

@implementation LoginViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    
    UIImageView *bgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, self.view.bounds.size.width, self.view.bounds.size.height)];
    bgView.image = [UIImage imageNamed:@"login_bg"];
    [self.view addSubview:bgView];

    //添加内容框背景图
    //距离顶部 211  347*420 居中
    UIImageView *contentBgView = [[UIImageView alloc] initWithFrame:CGRectMake(0,   0, 347, 420)];
    contentBgView.image = [UIImage imageNamed:@"login_content_bg"];
    contentBgView.userInteractionEnabled = YES;
    [self.view addSubview:contentBgView];
    [contentBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view);
        make.top.equalTo(self.view).offset(211);
        make.width.mas_equalTo(347);
        make.height.mas_equalTo(420);
    }];

    //文字 logo
    //Welcome Back! 
    //Your Financial Partner Is Ready to Help You Borrow Smarter.
    //位置 距离内容背景图顶部 35 居中 左右各 43 换行 字体颜色#242424  字体CeraGR-BlackItalic
    UILabel *logoLabel = [[UILabel alloc] init];
    logoLabel.text = @"Welcome Back!\nYour Financial Partner Is Ready to Help You Borrow Smarter.";
    logoLabel.numberOfLines = 0;
    logoLabel.textAlignment = NSTextAlignmentLeft;//靠左
    UIFontDescriptor *desc = [[UIFontDescriptor preferredFontDescriptorWithTextStyle:UIFontTextStyleTitle1]
        fontDescriptorWithSymbolicTraits:UIFontDescriptorTraitBold | UIFontDescriptorTraitItalic];
    logoLabel.font = [UIFont fontWithDescriptor:desc size:16];
    logoLabel.textColor = [UIColor colorWithHexString:@"#242424"];
    [contentBgView addSubview:logoLabel];
    [logoLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(contentBgView).offset(43);
        make.right.equalTo(contentBgView).offset(-43);
        make.top.equalTo(contentBgView).offset(35);
    }];
    
    //手机输入框背景，白色纯色 view 高度 50.圆角 25，左右距离各 24
    UIView *phoneBgView = [[UIView alloc] init];
    phoneBgView.backgroundColor = [UIColor whiteColor];
    phoneBgView.layer.cornerRadius = 25;
    [contentBgView addSubview:phoneBgView];
    [phoneBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(contentBgView);
        make.top.equalTo(logoLabel).offset(100);
        make.left.equalTo(contentBgView).offset(24);
        make.right.equalTo(contentBgView).offset(-24);
        make.height.mas_equalTo(50);
    }];
    
    // // 手机号输入框，放在手机背景view中，左边距离 56，占位文案Enter mobile code。右边和上下都贴近手机背景view
    // 无边框
    self.phoneTextField = [[UITextField alloc] init];
    self.phoneTextField.placeholder = @"Enter Mobile Code";
    self.phoneTextField.borderStyle = UITextBorderStyleNone;
    self.phoneTextField.keyboardType = UIKeyboardTypeNumberPad;
    [phoneBgView addSubview:self.phoneTextField];
    [self.phoneTextField mas_makeConstraints:^(MASConstraintMaker *make) {   
        make.left.equalTo(phoneBgView).offset(56);
        make.right.equalTo(phoneBgView).offset(0);
        make.top.equalTo(phoneBgView).offset(0);
        make.bottom.equalTo(phoneBgView).offset(0);
    }];

    //国家区号 label，在手机输入框左边的 56 距离中间。 字号 15 居中
    UILabel *countryCodeLabel = [[UILabel alloc] init];
    NSString *dialCode = [[NSUserDefaults standardUserDefaults] stringForKey:@"country_phone_code"] ?: @"63";
    countryCodeLabel.text = dialCode;    
    countryCodeLabel.textColor = [UIColor colorWithHexString:@"#242424"];
    countryCodeLabel.font = [UIFont systemFontOfSize:15];
    countryCodeLabel.textAlignment = NSTextAlignmentCenter;
    [phoneBgView addSubview:countryCodeLabel];
    [countryCodeLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(phoneBgView).offset(0);
        make.width.mas_equalTo(56);
        make.centerY.equalTo(phoneBgView);
    }];

    //验证码输入背景 同手机号背景
    UIView *codeBgView = [[UIView alloc] init];
    codeBgView.backgroundColor = [UIColor whiteColor];
    codeBgView.layer.cornerRadius = 25;
    [contentBgView addSubview:codeBgView];
    [codeBgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(contentBgView);
        make.top.equalTo(phoneBgView.mas_bottom).offset(15);
        make.left.equalTo(contentBgView).offset(24); 
        make.right.equalTo(contentBgView).offset(-24);
        make.height.mas_equalTo(50);
    }];

    //验证码输入框
    self.codeTextField = [[UITextField alloc] init];
    self.codeTextField.placeholder = @"Enter Dynamic Code";
    self.codeTextField.borderStyle = UITextBorderStyleNone;
    self.codeTextField.keyboardType = UIKeyboardTypeNumberPad;
    [codeBgView addSubview:self.codeTextField];
    [self.codeTextField mas_makeConstraints:^(MASConstraintMaker *make) {   
        make.left.equalTo(codeBgView).offset(12);
        make.right.equalTo(codeBgView).offset(-85);
        make.top.equalTo(codeBgView).offset(0);
        make.bottom.equalTo(codeBgView).offset(0);
    }];

    //发送验证码按钮，在验证码背景中，大小75*34，有配套按钮背景图，文案 Send，居中。右侧距离背景 View 10pt
    self.getCodeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.getCodeButton setTitle:@"Send" forState:UIControlStateNormal];
    [self.getCodeButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.getCodeButton setBackgroundImage:[UIImage imageNamed:@"login_send_code_bg"] forState:UIControlStateNormal];
    [self.getCodeButton addTarget:self action:@selector(getCodeAction) forControlEvents:UIControlEventTouchUpInside];
    [codeBgView addSubview:self.getCodeButton];
    [self.getCodeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerY.equalTo(codeBgView);
        make.right.equalTo(codeBgView).offset(-10);
        make.width.mas_equalTo(75);
        make.height.mas_equalTo(34);
    }];

    //新语音验证码按钮，上方距离验证码背景 20pt，大小 230*30，有配套按钮背景图，居中。是一个图片按钮，不需要文案和其他设置
    UIButton *newVoiceCodeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [newVoiceCodeButton setBackgroundImage:[UIImage imageNamed:@"login_new_voice_code_bg"] forState:UIControlStateNormal];
    [newVoiceCodeButton addTarget:self action:@selector(getVoiceCodeAction) forControlEvents:UIControlEventTouchUpInside];
    [contentBgView addSubview:newVoiceCodeButton];
    [newVoiceCodeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(contentBgView);
        make.top.equalTo(codeBgView.mas_bottom).offset(20);
        make.width.mas_equalTo(230);
        make.height.mas_equalTo(25);
    }];

    //登录按钮，下方距离新语音验证码按钮 41pt，大小 297 pt*55pt，有配套按钮背景图，文案 Log In，居中。 字号 20
    self.loginButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.loginButton setTitle:@"Log In" forState:UIControlStateNormal]; //文案加粗
    [self.loginButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    [self.loginButton setBackgroundImage:[UIImage imageNamed:@"login_login_bg"] forState:UIControlStateNormal];
    self.loginButton.titleLabel.font = [UIFont systemFontOfSize:20 weight:UIFontWeightBold];
    [self.loginButton addTarget:self action:@selector(loginButtonAction) forControlEvents:UIControlEventTouchUpInside];
    [contentBgView addSubview:self.loginButton];
    [self.loginButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(contentBgView);
        make.top.equalTo(newVoiceCodeButton.mas_bottom).offset(41);
        make.width.mas_equalTo(297);
        make.height.mas_equalTo(55);
    }];

    //协议按钮，有背景图不需要文案，大小 278pt*28pt，居中偏右8pt，可以点击跳转 H5 协议页面
    UIButton *protocolButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [protocolButton setBackgroundImage:[UIImage imageNamed:@"login_protocol_bg"] forState:UIControlStateNormal];
    [protocolButton addTarget:self action:@selector(protocolButtonAction) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:protocolButton];
    [protocolButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.centerX.equalTo(self.view).offset(8);
        make.top.equalTo(contentBgView.mas_bottom).offset(45);
        make.width.mas_equalTo(278);
        make.height.mas_equalTo(28);
    }];

    //隐私勾选按钮，在协议按钮左边 4pt 的地方，大小为 12*12，有 勾选和没勾选的图片切换，默认为勾选状态，切换为未勾选状态会导致登录按钮不能点击，不是居中对齐，是顶部对齐。
    self.privacyButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.privacyButton setImage:[UIImage imageNamed:@"login_privacy_selected"] forState:UIControlStateSelected];
    [self.privacyButton setImage:[UIImage imageNamed:@"login_privacy_unselected"] forState:UIControlStateNormal];
    [self.privacyButton addTarget:self action:@selector(privacyButtonAction) forControlEvents:UIControlEventTouchUpInside];
    self.privacyButton.selected = YES;
    self.loginButton.enabled = YES;
    [self.view addSubview:self.privacyButton];
    [self.privacyButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(protocolButton.mas_left).offset(-2);
        make.top.equalTo(protocolButton.mas_top).offset(0);
        make.width.mas_equalTo(20);
        make.height.mas_equalTo(20);
    }];

    // 注意：强制登录模式下不显示返回按钮

    // // 点击空白收起键盘
    UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(dismissKeyboard)];
    tap.cancelsTouchesInView = NO;
    [self.view addGestureRecognizer:tap];

    self.riskStartRegister = 0; // 初始化埋点开始时间
}

- (void)dismissKeyboard {
    [self.view endEditing:YES];
}

// 注意：强制登录模式下不需要返回按钮事件处理

- (void)loginButtonAction {
    NSString *phone = self.phoneTextField.text ?: @"";
    NSString *code = self.codeTextField.text ?: @"";
    
    // 验证手机号和验证码是否为空
    if (phone.length == 0) {
        // 判空提示延长至 3 秒
        [self showCodeToast:@"Please enter mobile number"];
        return;
    }
    
    if (code.length == 0) {
        // 判空提示延长至 3 秒
        [self showCodeToast:@"Please enter verification code"];
        return;
    }
    
    __weak typeof(self) weakSelf = self;
    [self loginWithPhone:phone code:code completion:^(BOOL success, NSString *msg) {
        if (msg.length > 0) {
            [HUD showToast:msg inView:self.view];
        }
    }];
}

// 协议按钮点击事件
- (void)protocolButtonAction {
    NSString *urlString = [[NSUserDefaults standardUserDefaults] stringForKey:@"protocol_url"];
    if (urlString.length == 0) {
        // 后端未下发时使用占位链接，便于调试
        urlString = @"https://www.google.com";
    }

    H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:urlString];
    if (self.navigationController) {
        [self.navigationController pushViewController:webVC animated:YES];
    } else {
        UINavigationController *nav = [[UINavigationController alloc] initWithRootViewController:webVC];
        nav.modalPresentationStyle = UIModalPresentationFullScreen;
        [self presentViewController:nav animated:YES completion:nil];
    }
}

// 隐私勾选按钮点击事件
- (void)privacyButtonAction {
    self.privacyButton.selected = !self.privacyButton.selected;
    self.loginButton.enabled = self.privacyButton.selected;
}

- (void)getCodeAction {
    // 验证手机号是否为空
    NSString *phone = self.phoneTextField.text;
    if (phone.length == 0) {
        // 判空提示延长至 3 秒
        [self showCodeToast:@"Please enter mobile number"];
        return;
    }

    // 记录埋点开始时间（仅首次）
    if (self.riskStartRegister == 0) {
        self.riskStartRegister = [[NSDate date] timeIntervalSince1970];
        NSLog(@"[埋点测试8-2] 埋点1开始时间已记录: %.0f", self.riskStartRegister);
    }

    // 点击后立即进入倒计时，提升用户反馈
    self.getCodeButton.enabled = NO;
    self.secondsLeft = 60;
    [self.getCodeButton setTitle:[NSString stringWithFormat:@"%lds", (long)self.secondsLeft] forState:UIControlStateDisabled];
    if (self.timer) {
        [self.timer invalidate];
    }
    self.timer = [NSTimer scheduledTimerWithTimeInterval:1.0 target:self selector:@selector(updateTimer) userInfo:nil repeats:YES];

    // Show loading HUD while requesting verification code
    UIView *hudSuperView = self.view.window ?: self.view;
    [HUD showLoadingInView:hudSuperView withMessage:@"Sending…"];

    // 发送网络请求
    NSDictionary *params = @{@"slabs": phone};
    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/ofgwendoline" params:params completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [HUD hideForView:hudSuperView];
            NSNumber *modest = response[@"modest"];
            NSString *msg = response[@"patted"] ?: @"";

            // 如果接口失败或返回非 0，则取消倒计时并复位按钮
            if (error || !([modest respondsToSelector:@selector(integerValue)] && [modest integerValue] == 0)) {
                [weakSelf.timer invalidate];
                weakSelf.timer = nil;
                [weakSelf resetCodeButton];
            }

            if (msg.length > 0) {
                // 发送验证码提示单独处理，停留 3 秒
                [weakSelf showCodeToast:msg];
            }
        });
    }];
}

- (void)updateTimer {
    self.secondsLeft--;
    if (self.secondsLeft > 0) {
        [self.getCodeButton setTitle:[NSString stringWithFormat:@"%lds", (long)self.secondsLeft] forState:UIControlStateDisabled];
    } else {
        [self.timer invalidate];
        self.timer = nil;
        [self resetCodeButton];
    }
}

- (void)resetCodeButton {
    self.getCodeButton.enabled = YES;
    [self.getCodeButton setTitle:@"Send" forState:UIControlStateNormal];
}

- (void)getVoiceCodeAction {
    // 验证手机号是否为空
    NSString *phone = self.phoneTextField.text;
    if (phone.length == 0) {
        // 判空提示延长至 3 秒
        [self showCodeToast:@"Please enter mobile number"];
        return;
    }
    
    // 记录埋点开始时间 (语音验证码也视为埋点1起点)，仅首次
    if (self.riskStartRegister == 0) {
        self.riskStartRegister = [[NSDate date] timeIntervalSince1970];
        NSLog(@"[埋点测试8-2] 埋点1开始时间已记录(语音): %.0f", self.riskStartRegister);
    }
    
    NSDictionary *params = @{@"slabs": phone};
    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/outright" params:params completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            NSString *msg = response[@"patted"] ?: @"";
            if (msg.length > 0) {
                // 语音验证码提示单独处理，停留 3 秒
                [weakSelf showCodeToast:msg];
            }
        });
    }];
}

- (void)loginWithPhone:(NSString *)phone code:(NSString *)code completion:(void (^)(BOOL success, NSString *msg))completion {
    NSTimeInterval endTs = [[NSDate date] timeIntervalSince1970];

    // Show loading HUD while logging in
    UIView *hudSuperView = self.view.window ?: self.view;
    [HUD showLoadingInView:hudSuperView withMessage:@"Logging in…"];

    NSDictionary *params = @{@"tarts": phone ?: @"", @"jam": code ?: @""};
    [NetworkManager postFormWithAPI:@"Alicia/modest" params:params completion:^(NSDictionary *response, NSError *error) {
        // Hide loading HUD once response is received
        dispatch_async(dispatch_get_main_queue(), ^{
            [HUD hideForView:hudSuperView];
        });
        if (error) {
            if (completion) completion(NO, error.localizedDescription ?: @"网络错误");
            return;
        }
        NSNumber *modest = response[@"modest"];
        NSLog(@"[埋点测试8-2] 登录接口返回 modest: %@", modest);
        if ([modest respondsToSelector:@selector(integerValue)] && [modest integerValue] == 0) {
            NSLog(@"[埋点测试8-2] 登录成功，准备上报埋点1");
            NSDictionary *awkward = response[@"awkward"];
            NSString *token = awkward[@"andwere"];
            if (token.length > 0) {
                // 先保存 token，确保后续埋点请求能够携带 andwere 公参
                [[NSUserDefaults standardUserDefaults] setObject:token forKey:@"token"];
                [[NSUserDefaults standardUserDefaults] synchronize];
            }

            // 注册/登录成功 -> 上报埋点1（此时 token 已写入，NetworkManager 能够拼接 andwere）
            NSTimeInterval startTs = self.riskStartRegister > 0 ? self.riskStartRegister : endTs;
            NSLog(@"[埋点测试8-2] 埋点1开始上报 startTime:%.0f endTime:%.0f", startTs, endTs);
            [RiskEventManager reportEventType:RiskEventTypeRegister startTime:startTs endTime:endTs orderId:nil];

            if (token.length > 0) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self dismissViewControllerAnimated:YES completion:nil];
                });
            }
            if (completion) completion(YES, response[@"patted"] ?: @"success");
        } else {
            NSLog(@"[埋点测试8-2] 登录失败，不上报埋点1，modest: %@", modest);
            NSString *msg = response[@"patted"] ?: @"";
            if (msg.length > 0) {
                [HUD showToast:msg inView:self.view];
            }
            if (completion) completion(NO, msg);
        }
    }];
}

#pragma mark - Private Helpers

/// 显示验证码相关提示，展示时长 3 秒
- (void)showCodeToast:(NSString *)message {
    if (message.length == 0) { return; }
    MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.view animated:YES];
    hud.mode = MBProgressHUDModeText;
    hud.label.text = message;
    hud.removeFromSuperViewOnHide = YES;
    hud.margin = 16.0;
    hud.label.numberOfLines = 0;
    hud.userInteractionEnabled = NO;
    [hud hideAnimated:YES afterDelay:3.0];
}

#pragma mark - Cleanup

- (void)dealloc {
    [self.timer invalidate];
    self.timer = nil;
}

@end
